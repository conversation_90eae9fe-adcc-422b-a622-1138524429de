# 🖱️ 智能按键精灵 Pro - 鼠标自动化工具

一个功能强大、界面美观的鼠标自动点击工具，支持录制和回放鼠标点击操作。

## ✨ 功能特性

- 🖱️ **实时鼠标位置显示** - 高精度实时显示当前鼠标坐标
- 📹 **智能录制功能** - 录制鼠标点击位置和时间间隔
- 🖱️ **鼠标中键快速录制** - 录制时使用鼠标中键快速添加当前位置
- ▶️ **自动回放功能** - 精确回放录制的点击序列
- 💾 **动作保存与管理** - 保存录制的动作，支持命名和描述
- 🎮 **自定义热键系统** - 为每个保存的动作设置专属热键
- 🎹 **全局热键控制** - F9开始回放，F10停止回放（后台运行时也可用）
- 📋 **动作列表管理** - 查看、编辑、删除保存的动作
- 🔍 **动作详情预览** - 点击动作查看详细步骤和统计信息
- ⏱️ **自定义延迟** - 可设置点击间隔时间（0.1-10秒）
- 📊 **扩展步骤支持** - 最多支持50步操作
- 🔄 **倒计时启动** - 回放前3秒倒计时准备
- 📝 **详细操作列表** - 显示所有录制的操作详情
- 🎨 **现代化界面** - 美观的双面板用户界面设计
- 🚀 **智能状态管理** - 实时状态反馈和按钮状态管理
- 💿 **数据持久化** - 自动保存和加载动作数据

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. **启动程序**
   ```bash
   python main.py
   ```

2. **录制操作**
   - 点击"🔴 开始录制"按钮
   - 将鼠标移动到要点击的位置
   - 方法一：点击"➕ 添加位置"按钮
   - 方法二：直接按鼠标中键快速添加当前位置
   - 重复上述步骤，最多可录制50步
   - 点击"⏹️ 停止录制"完成录制

3. **保存动作**
   - 录制完成后，点击"💾 保存动作"按钮
   - 输入动作名称和描述（可选）
   - 动作将自动保存到文件中

4. **设置延迟**
   - 在"点击间隔"输入框中设置每次点击之间的延迟时间
   - 支持0.1到10秒的延迟设置

5. **动作管理**
   - 在右侧面板查看所有保存的动作
   - 点击动作名称查看详细步骤和统计信息
   - 为动作设置自定义热键（A-Z、0-9、F1-F8、F11-F12）
   - 执行或删除保存的动作

6. **回放操作**
   - 确保已录制了操作步骤
   - 方法一：点击"▶️ 开始回放"按钮（回放当前录制）
   - 方法二：按 `F9` 键全局热键启动回放（回放当前录制）
   - 方法三：在动作列表中点击"▶️ 执行动作"（回放保存的动作）
   - 方法四：按设置的自定义热键（回放对应的保存动作）
   - 程序会显示3秒倒计时
   - 倒计时结束后自动执行录制的操作

7. **热键控制**
   - **F9键**: 开始回放当前录制（全局热键，程序后台运行时也可用）
   - **F10键**: 停止回放（全局热键，程序后台运行时也可用）
   - **自定义热键**: 执行对应的保存动作（全局热键，程序后台运行时也可用）
   - 热键在程序最小化或后台运行时依然有效

8. **其他功能**
   - **🗑️ 清空录制**: 删除所有已录制的操作
   - **⏹️ 停止回放**: 中途停止正在进行的回放
   - **📍 实时位置**: 界面顶部实时显示鼠标坐标
   - **🖱️ 中键快捷**: 录制时使用鼠标中键快速添加位置
   - **💿 自动保存**: 程序启动时自动加载之前保存的动作

## 安全提示

- 程序启用了pyautogui的安全模式，将鼠标移动到屏幕左上角可紧急停止
- 回放前有3秒准备时间，可以取消操作
- 建议在测试环境中先试用

## 文件说明

- `main.py` - 主程序文件，包含GUI界面
- `mouse_recorder.py` - 鼠标操作录制和回放核心模块
- `requirements.txt` - Python依赖包列表
- `README.md` - 使用说明文档

## 系统要求

- Python 3.6+
- Windows/macOS/Linux
- 支持图形界面的环境

## 🔥 新功能亮点

### 💾 动作保存与管理系统
- **动作保存**: 录制完成后可保存为命名动作，支持添加描述
- **动作列表**: 右侧面板显示所有保存的动作，包含使用统计
- **动作预览**: 点击动作查看详细步骤、创建时间、使用次数等信息
- **数据持久化**: 自动保存到JSON文件，程序重启后自动加载

### 🎮 自定义热键系统
- **专属热键**: 为每个保存的动作设置独特的热键
- **丰富选择**: 支持A-Z、0-9、F1-F8、F11-F12等热键
- **冲突检测**: 自动防止热键冲突，智能提示可用热键
- **全局响应**: 热键在程序后台运行时也能响应

### 🎹 增强的全局热键控制
- **F9键**: 全局开始回放当前录制，即使程序在后台运行也能响应
- **F10键**: 全局停止回放，紧急情况下快速停止
- **自定义热键**: 直接执行对应的保存动作
- 无需切换到程序窗口，随时随地控制回放

### 🖱️ 鼠标中键快速录制
- 开始录制后，只需按鼠标中键即可快速添加当前鼠标位置
- 无需点击界面按钮，操作更加流畅高效
- 支持连续快速录制多个位置

### 📊 扩展到50步操作
- 从原来的20步扩展到50步，支持更复杂的自动化任务
- 智能步数管理，实时显示当前录制进度

### 🎨 全新双面板界面
- **左侧面板**: 录制控制、状态显示、当前步骤预览
- **右侧面板**: 动作管理、详情预览、热键设置
- 采用现代化设计语言，界面更加美观
- 智能色彩系统，不同状态用不同颜色标识

## 注意事项

1. 首次使用时可能需要授予程序鼠标控制权限
2. 在macOS上可能需要在"系统偏好设置 > 安全性与隐私 > 辅助功能"中添加Python或终端的权限
3. 录制的坐标是绝对坐标，在不同分辨率的屏幕上可能需要重新录制
4. 建议在录制前关闭不必要的程序，避免误操作
5. 使用鼠标中键录制时，确保鼠标中键功能正常
6. 全局热键功能需要程序有足够的系统权限
7. 在某些安全软件环境下，热键功能可能需要添加程序到白名单

## 故障排除

**问题**: 程序无法控制鼠标
**解决**: 检查系统权限设置，确保Python有辅助功能权限

**问题**: 点击位置不准确
**解决**: 确保录制和回放时屏幕分辨率一致

**问题**: 鼠标中键不响应
**解决**: 检查鼠标中键是否正常工作，或尝试重启程序

**问题**: 热键F9/F10不响应
**解决**: 检查程序是否有足够权限，或尝试以管理员身份运行程序

**问题**: 程序崩溃
**解决**: 检查是否正确安装了所有依赖包，特别是pynput库

## 🚀 快速开始示例

### 基础使用流程
1. 启动程序：`python main.py`
2. 点击"🔴 开始录制"
3. 将鼠标移动到第一个目标位置，按鼠标中键
4. 移动到第二个位置，再按鼠标中键
5. 继续添加更多位置...
6. 点击"⏹️ 停止录制"
7. 点击"💾 保存动作"，输入名称如"自动登录"
8. 点击"▶️ 开始回放"观看自动化执行

### 动作管理流程
1. 在右侧面板查看保存的动作列表
2. 点击动作名称查看详细步骤
3. 在热键下拉框中选择一个热键（如"a"）
4. 点击"设置热键"为动作绑定热键
5. 现在按 `A` 键即可执行该动作

### 热键使用流程
1. 完成录制和保存后，可以最小化程序窗口
2. 在任何时候按 `F9` 键开始回放当前录制
3. 按设置的自定义热键（如 `A`）执行对应的保存动作
4. 回放过程中按 `F10` 键可立即停止
5. 无需切换到程序窗口，全局控制更便捷

### 高级使用技巧
- 为不同的任务创建多个动作（如"自动登录"、"数据录入"、"游戏操作"等）
- 为每个动作设置容易记忆的热键
- 利用动作描述功能记录动作用途
- 查看使用统计了解哪些动作最常用

享受智能自动化带来的便利！ 🎉
