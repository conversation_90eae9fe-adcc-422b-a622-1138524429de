import json
import os
from typing import Dict, List, Tuple, Optional
from datetime import datetime

class ActionManager:
    def __init__(self, data_file: str = "actions.json"):
        self.data_file = data_file
        self.actions: Dict[str, Dict] = {}
        self.load_actions()
    
    def load_actions(self):
        """从文件加载动作数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.actions = json.load(f)
                print(f"✅ 成功加载 {len(self.actions)} 个保存的动作")
            else:
                self.actions = {}
                print("📁 动作文件不存在，创建新的动作库")
        except Exception as e:
            print(f"❌ 加载动作文件失败: {e}")
            self.actions = {}
    
    def save_actions(self):
        """保存动作数据到文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.actions, f, ensure_ascii=False, indent=2)
            print(f"💾 成功保存 {len(self.actions)} 个动作到文件")
            return True
        except Exception as e:
            print(f"❌ 保存动作文件失败: {e}")
            return False
    
    def add_action(self, name: str, steps: List[Tuple[int, int, float]], 
                   hotkey: str = None, description: str = "") -> bool:
        """添加新动作"""
        if not name or not steps:
            return False
        
        # 检查热键冲突
        if hotkey and self.is_hotkey_used(hotkey, exclude_name=name):
            return False
        
        action_data = {
            'name': name,
            'steps': steps,
            'hotkey': hotkey,
            'description': description,
            'created_time': datetime.now().isoformat(),
            'last_used': None,
            'use_count': 0
        }
        
        self.actions[name] = action_data
        return self.save_actions()
    
    def remove_action(self, name: str) -> bool:
        """删除动作"""
        if name in self.actions:
            del self.actions[name]
            return self.save_actions()
        return False
    
    def get_action(self, name: str) -> Optional[Dict]:
        """获取指定动作"""
        return self.actions.get(name)
    
    def get_all_actions(self) -> Dict[str, Dict]:
        """获取所有动作"""
        return self.actions.copy()
    
    def get_action_names(self) -> List[str]:
        """获取所有动作名称"""
        return list(self.actions.keys())
    
    def update_action_hotkey(self, name: str, hotkey: str) -> bool:
        """更新动作的热键"""
        if name not in self.actions:
            return False
        
        # 检查热键冲突
        if hotkey and self.is_hotkey_used(hotkey, exclude_name=name):
            return False
        
        self.actions[name]['hotkey'] = hotkey
        return self.save_actions()
    
    def is_hotkey_used(self, hotkey: str, exclude_name: str = None) -> bool:
        """检查热键是否已被使用"""
        for name, action in self.actions.items():
            if name != exclude_name and action.get('hotkey') == hotkey:
                return True
        return False
    
    def get_action_by_hotkey(self, hotkey: str) -> Optional[Dict]:
        """根据热键获取动作"""
        for action in self.actions.values():
            if action.get('hotkey') == hotkey:
                return action
        return None
    
    def update_usage_stats(self, name: str):
        """更新动作使用统计"""
        if name in self.actions:
            self.actions[name]['last_used'] = datetime.now().isoformat()
            self.actions[name]['use_count'] = self.actions[name].get('use_count', 0) + 1
            self.save_actions()
    
    def get_action_steps_text(self, name: str) -> str:
        """获取动作步骤的文本描述"""
        action = self.get_action(name)
        if not action:
            return "动作不存在"
        
        steps = action.get('steps', [])
        if not steps:
            return "暂无步骤"
        
        result = []
        for i, (x, y, delay) in enumerate(steps):
            result.append(f"第{i+1}步: 点击 ({x}, {y}) 延迟 {delay}秒")
        
        return "\n".join(result)
    
    def export_action(self, name: str, file_path: str) -> bool:
        """导出单个动作到文件"""
        action = self.get_action(name)
        if not action:
            return False
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump({name: action}, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"❌ 导出动作失败: {e}")
            return False
    
    def import_action(self, file_path: str) -> bool:
        """从文件导入动作"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_actions = json.load(f)
            
            for name, action in imported_actions.items():
                # 检查名称冲突
                original_name = name
                counter = 1
                while name in self.actions:
                    name = f"{original_name}_{counter}"
                    counter += 1
                
                # 清除热键（避免冲突）
                action['hotkey'] = None
                action['imported_time'] = datetime.now().isoformat()
                
                self.actions[name] = action
            
            return self.save_actions()
        except Exception as e:
            print(f"❌ 导入动作失败: {e}")
            return False
    
    def get_available_hotkeys(self) -> List[str]:
        """获取可用的热键列表"""
        all_hotkeys = []
        
        # 字母键
        for i in range(26):
            all_hotkeys.append(chr(ord('a') + i))
        
        # 数字键
        for i in range(10):
            all_hotkeys.append(str(i))
        
        # 功能键（排除F9和F10，它们被系统占用）
        for i in range(1, 13):
            if i not in [9, 10]:  # 排除F9和F10
                all_hotkeys.append(f"f{i}")
        
        # 过滤已使用的热键
        used_hotkeys = {action.get('hotkey') for action in self.actions.values() if action.get('hotkey')}
        available_hotkeys = [key for key in all_hotkeys if key not in used_hotkeys]
        
        return available_hotkeys
