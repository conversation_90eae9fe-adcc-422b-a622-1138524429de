import pyautogui
import time
import threading
from typing import List, Tuple, Callable, Optional, Dict
from pynput import mouse, keyboard
from action_manager import ActionManager

class MouseRecorder:
    def __init__(self):
        self.actions: List[Tuple[int, int, float]] = []  # (x, y, delay)
        self.is_recording = False
        self.is_playing = False
        self.max_steps = 50  # 增加到50步
        self.mouse_listener = None
        self.keyboard_listener = None
        self.middle_click_callback = None
        self.hotkey_callbacks = {
            'start_playback': None,
            'stop_playback': None
        }

        # 动作管理器
        self.action_manager = ActionManager()
        self.custom_hotkey_callback = None

        # 设置pyautogui的安全设置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
    
    def set_middle_click_callback(self, callback):
        """设置鼠标中键点击回调函数"""
        self.middle_click_callback = callback

    def set_hotkey_callbacks(self, start_callback, stop_callback):
        """设置热键回调函数"""
        self.hotkey_callbacks['start_playback'] = start_callback
        self.hotkey_callbacks['stop_playback'] = stop_callback

        # 启动键盘监听器
        self.start_keyboard_listener()

    def set_custom_hotkey_callback(self, callback):
        """设置自定义热键回调函数"""
        self.custom_hotkey_callback = callback

    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件处理"""
        if pressed and button == mouse.Button.middle and self.is_recording:
            if self.middle_click_callback:
                self.middle_click_callback()

    def _on_key_press(self, key):
        """键盘按键事件处理"""
        try:
            # F9 - 开始回放
            if key == keyboard.Key.f9:
                if self.hotkey_callbacks['start_playback']:
                    self.hotkey_callbacks['start_playback']()

            # F10 - 停止回放
            elif key == keyboard.Key.f10:
                if self.hotkey_callbacks['stop_playback']:
                    self.hotkey_callbacks['stop_playback']()

            # 处理自定义热键
            else:
                key_str = self._key_to_string(key)
                if key_str and self.custom_hotkey_callback:
                    action = self.action_manager.get_action_by_hotkey(key_str)
                    if action:
                        self.custom_hotkey_callback(action)

        except AttributeError:
            # 处理特殊按键
            pass

    def _key_to_string(self, key) -> str:
        """将按键对象转换为字符串"""
        try:
            # 字母和数字键
            if hasattr(key, 'char') and key.char:
                return key.char.lower()

            # 功能键
            if key == keyboard.Key.f1:
                return 'f1'
            elif key == keyboard.Key.f2:
                return 'f2'
            elif key == keyboard.Key.f3:
                return 'f3'
            elif key == keyboard.Key.f4:
                return 'f4'
            elif key == keyboard.Key.f5:
                return 'f5'
            elif key == keyboard.Key.f6:
                return 'f6'
            elif key == keyboard.Key.f7:
                return 'f7'
            elif key == keyboard.Key.f8:
                return 'f8'
            elif key == keyboard.Key.f11:
                return 'f11'
            elif key == keyboard.Key.f12:
                return 'f12'

            return None
        except:
            return None

    def start_keyboard_listener(self):
        """启动键盘监听器"""
        if self.keyboard_listener is None or not self.keyboard_listener.running:
            self.keyboard_listener = keyboard.Listener(on_press=self._on_key_press)
            self.keyboard_listener.start()

    def stop_keyboard_listener(self):
        """停止键盘监听器"""
        if self.keyboard_listener and self.keyboard_listener.running:
            self.keyboard_listener.stop()
            self.keyboard_listener = None

    def start_recording(self):
        """开始录制鼠标点击"""
        if self.is_recording:
            return False

        self.actions.clear()
        self.is_recording = True

        # 启动鼠标监听器
        if self.mouse_listener is None or not self.mouse_listener.running:
            self.mouse_listener = mouse.Listener(on_click=self._on_mouse_click)
            self.mouse_listener.start()

        return True
    
    def stop_recording(self):
        """停止录制"""
        self.is_recording = False

        # 停止鼠标监听器
        if self.mouse_listener and self.mouse_listener.running:
            self.mouse_listener.stop()
            self.mouse_listener = None
    
    def add_click(self, x: int, y: int, delay: float = 1.0):
        """添加一个点击动作"""
        if len(self.actions) >= self.max_steps:
            return False
        
        self.actions.append((x, y, delay))
        return True
    
    def get_current_mouse_position(self):
        """获取当前鼠标位置"""
        return pyautogui.position()
    
    def record_current_position(self, delay: float = 1.0):
        """录制当前鼠标位置"""
        if not self.is_recording or len(self.actions) >= self.max_steps:
            return False
        
        x, y = self.get_current_mouse_position()
        return self.add_click(x, y, delay)
    
    def play_actions(self, callback=None):
        """回放录制的动作"""
        if self.is_playing or not self.actions:
            return False
        
        def play_thread():
            self.is_playing = True
            try:
                for i, (x, y, delay) in enumerate(self.actions):
                    if not self.is_playing:  # 检查是否被停止
                        break
                    
                    # 执行点击
                    pyautogui.click(x, y)
                    
                    if callback:
                        callback(f"执行第 {i+1} 步: 点击 ({x}, {y})")
                    
                    # 等待延迟时间（除了最后一步）
                    if i < len(self.actions) - 1:
                        time.sleep(delay)
                
                if callback:
                    callback("回放完成！")
            
            except Exception as e:
                if callback:
                    callback(f"回放出错: {str(e)}")
            
            finally:
                self.is_playing = False
        
        # 在新线程中执行回放
        thread = threading.Thread(target=play_thread)
        thread.daemon = True
        thread.start()
        return True
    
    def stop_playing(self):
        """停止回放"""
        self.is_playing = False
    
    def clear_actions(self):
        """清空所有动作"""
        if not self.is_recording and not self.is_playing:
            self.actions.clear()
            return True
        return False
    
    def get_actions_count(self):
        """获取当前动作数量"""
        return len(self.actions)
    
    def get_actions_list(self):
        """获取动作列表的字符串表示"""
        if not self.actions:
            return "暂无录制的动作"
        
        result = []
        for i, (x, y, delay) in enumerate(self.actions):
            result.append(f"第{i+1}步: 点击 ({x}, {y}) 延迟 {delay}秒")
        
        return "\n".join(result)
    
    def update_delay(self, index: int, new_delay: float):
        """更新指定步骤的延迟时间"""
        if 0 <= index < len(self.actions):
            x, y, _ = self.actions[index]
            self.actions[index] = (x, y, new_delay)
            return True
        return False
    
    def remove_action(self, index: int):
        """删除指定的动作"""
        if 0 <= index < len(self.actions) and not self.is_recording and not self.is_playing:
            self.actions.pop(index)
            return True
        return False

    def play_saved_action(self, action_name: str, callback=None):
        """回放保存的动作"""
        if self.is_playing:
            return False

        action = self.action_manager.get_action(action_name)
        if not action:
            return False

        steps = action.get('steps', [])
        if not steps:
            return False

        def play_thread():
            self.is_playing = True
            try:
                for i, (x, y, delay) in enumerate(steps):
                    if not self.is_playing:  # 检查是否被停止
                        break

                    # 执行点击
                    pyautogui.click(x, y)

                    if callback:
                        callback(f"执行 {action_name} 第 {i+1} 步: 点击 ({x}, {y})")

                    # 等待延迟时间（除了最后一步）
                    if i < len(steps) - 1:
                        time.sleep(delay)

                # 更新使用统计
                self.action_manager.update_usage_stats(action_name)

                if callback:
                    callback(f"动作 {action_name} 回放完成！")

            except Exception as e:
                if callback:
                    callback(f"回放动作 {action_name} 出错: {str(e)}")

            finally:
                self.is_playing = False

        # 在新线程中执行回放
        thread = threading.Thread(target=play_thread)
        thread.daemon = True
        thread.start()
        return True
