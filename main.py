import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from mouse_recorder import MouseRecorder

class MouseAutomationGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🖱️ 智能按键精灵 Pro - 鼠标自动化工具")
        self.root.geometry("750x650")
        self.root.resizable(True, True)

        # 设置现代化主题色彩
        self.colors = {
            'primary': '#2E86AB',      # 主色调 - 蓝色
            'secondary': '#A23B72',    # 次要色 - 紫红色
            'success': '#F18F01',      # 成功色 - 橙色
            'warning': '#C73E1D',      # 警告色 - 红色
            'background': '#F5F7FA',   # 背景色 - 浅灰
            'surface': '#FFFFFF',      # 表面色 - 白色
            'text_primary': '#2D3748', # 主文本色
            'text_secondary': '#718096' # 次要文本色
        }

        # 设置窗口背景色
        self.root.configure(bg=self.colors['background'])

        # 创建鼠标录制器
        self.recorder = MouseRecorder()

        # 设置中键点击回调
        self.recorder.set_middle_click_callback(self.on_middle_click)

        # 配置样式
        self.setup_styles()

        # 创建界面
        self.create_widgets()

        # 启动位置更新线程
        self.start_position_update()

    def setup_styles(self):
        """配置现代化样式"""
        style = ttk.Style()

        # 配置按钮样式
        style.configure('Primary.TButton',
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Microsoft YaHei UI', 10, 'bold'))

        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Microsoft YaHei UI', 10, 'bold'))

        style.configure('Warning.TButton',
                       background=self.colors['warning'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Microsoft YaHei UI', 10, 'bold'))

        # 配置标签框样式
        style.configure('Modern.TLabelframe',
                       background=self.colors['surface'],
                       borderwidth=1,
                       relief='solid')

        style.configure('Modern.TLabelframe.Label',
                       background=self.colors['surface'],
                       foreground=self.colors['text_primary'],
                       font=('Microsoft YaHei UI', 11, 'bold'))

    def on_middle_click(self):
        """处理鼠标中键点击事件"""
        if self.recorder.is_recording:
            # 添加当前位置
            try:
                delay = float(self.delay_var.get())
            except ValueError:
                delay = 1.0
                self.delay_var.set("1.0")

            if self.recorder.record_current_position(delay):
                self.update_actions_display()
                self.update_step_count()

                # 显示提示信息
                x, y = self.recorder.get_current_mouse_position()
                self.status_label.config(
                    text=f"✅ 中键添加: ({x}, {y}) - 第{self.recorder.get_actions_count()}步",
                    foreground=self.colors['success']
                )

                # 检查是否达到最大步数
                if self.recorder.get_actions_count() >= 50:
                    messagebox.showinfo("提示", "已达到最大步数限制(50步)")
                    self.toggle_recording()

    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = tk.Frame(self.root, bg=self.colors['background'], padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(fill=tk.X, pady=(0, 25))

        # 主标题
        title_label = tk.Label(title_frame,
                              text="🖱️ 智能按键精灵 Pro",
                              font=("Microsoft YaHei UI", 24, "bold"),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack()

        # 副标题
        subtitle_label = tk.Label(title_frame,
                                 text="支持50步操作 • 鼠标中键快速录制 • 智能自动化",
                                 font=("Microsoft YaHei UI", 11),
                                 fg=self.colors['text_secondary'],
                                 bg=self.colors['background'])
        subtitle_label.pack(pady=(5, 0))

        # 鼠标位置显示区域
        position_frame = tk.Frame(main_frame, bg=self.colors['surface'], relief='solid', bd=1)
        position_frame.pack(fill=tk.X, pady=(0, 15))

        position_title = tk.Label(position_frame,
                                 text="📍 实时鼠标位置",
                                 font=("Microsoft YaHei UI", 12, "bold"),
                                 fg=self.colors['text_primary'],
                                 bg=self.colors['surface'])
        position_title.pack(pady=(10, 5))

        self.position_label = tk.Label(position_frame,
                                      text="X: 0, Y: 0",
                                      font=("Consolas", 14, "bold"),
                                      fg=self.colors['primary'],
                                      bg=self.colors['surface'])
        self.position_label.pack(pady=(0, 10))

        # 录制控制区域
        record_frame = tk.Frame(main_frame, bg=self.colors['surface'], relief='solid', bd=1)
        record_frame.pack(fill=tk.X, pady=(0, 15))

        record_title = tk.Label(record_frame,
                               text="🎬 录制控制中心",
                               font=("Microsoft YaHei UI", 12, "bold"),
                               fg=self.colors['text_primary'],
                               bg=self.colors['surface'])
        record_title.pack(pady=(15, 10))

        # 录制按钮行
        record_btn_frame = tk.Frame(record_frame, bg=self.colors['surface'])
        record_btn_frame.pack(pady=(0, 10))

        self.record_btn = tk.Button(record_btn_frame,
                                   text="🔴 开始录制",
                                   command=self.toggle_recording,
                                   font=("Microsoft YaHei UI", 11, "bold"),
                                   bg=self.colors['primary'],
                                   fg='white',
                                   relief='flat',
                                   padx=20, pady=8,
                                   cursor='hand2')
        self.record_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.add_click_btn = tk.Button(record_btn_frame,
                                      text="➕ 添加位置",
                                      command=self.add_current_position,
                                      font=("Microsoft YaHei UI", 10),
                                      bg=self.colors['success'],
                                      fg='white',
                                      relief='flat',
                                      padx=15, pady=8,
                                      cursor='hand2')
        self.add_click_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_btn = tk.Button(record_btn_frame,
                                  text="🗑️ 清空",
                                  command=self.clear_recording,
                                  font=("Microsoft YaHei UI", 10),
                                  bg=self.colors['warning'],
                                  fg='white',
                                  relief='flat',
                                  padx=15, pady=8,
                                  cursor='hand2')
        self.clear_btn.pack(side=tk.LEFT)

        # 延迟设置和提示
        delay_frame = tk.Frame(record_frame, bg=self.colors['surface'])
        delay_frame.pack(pady=(0, 15))

        delay_label = tk.Label(delay_frame,
                              text="⏱️ 点击间隔:",
                              font=("Microsoft YaHei UI", 10),
                              fg=self.colors['text_primary'],
                              bg=self.colors['surface'])
        delay_label.pack(side=tk.LEFT)

        self.delay_var = tk.StringVar(value="1.0")
        delay_spinbox = tk.Spinbox(delay_frame,
                                  from_=0.1, to=10.0, increment=0.1,
                                  width=8, textvariable=self.delay_var,
                                  font=("Consolas", 10))
        delay_spinbox.pack(side=tk.LEFT, padx=(10, 15))

        tip_label = tk.Label(delay_frame,
                            text="💡 提示: 录制时可使用鼠标中键快速添加位置",
                            font=("Microsoft YaHei UI", 9),
                            fg=self.colors['text_secondary'],
                            bg=self.colors['surface'])
        tip_label.pack(side=tk.LEFT)

        # 状态显示区域
        status_frame = tk.Frame(main_frame, bg=self.colors['surface'], relief='solid', bd=1)
        status_frame.pack(fill=tk.X, pady=(0, 15))

        status_title = tk.Label(status_frame,
                               text="📊 状态信息",
                               font=("Microsoft YaHei UI", 12, "bold"),
                               fg=self.colors['text_primary'],
                               bg=self.colors['surface'])
        status_title.pack(pady=(15, 10))

        status_content_frame = tk.Frame(status_frame, bg=self.colors['surface'])
        status_content_frame.pack(pady=(0, 15))

        self.status_label = tk.Label(status_content_frame,
                                    text="🟢 就绪",
                                    font=("Microsoft YaHei UI", 11, "bold"),
                                    fg=self.colors['success'],
                                    bg=self.colors['surface'])
        self.status_label.pack()

        self.step_label = tk.Label(status_content_frame,
                                  text="已录制: 0/50 步",
                                  font=("Microsoft YaHei UI", 10),
                                  fg=self.colors['text_secondary'],
                                  bg=self.colors['surface'])
        self.step_label.pack(pady=(5, 0))

        # 动作列表显示
        actions_frame = tk.Frame(main_frame, bg=self.colors['surface'], relief='solid', bd=1)
        actions_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        actions_title = tk.Label(actions_frame,
                                text="📝 录制的动作列表",
                                font=("Microsoft YaHei UI", 12, "bold"),
                                fg=self.colors['text_primary'],
                                bg=self.colors['surface'])
        actions_title.pack(pady=(15, 10))

        # 创建文本框容器
        text_container = tk.Frame(actions_frame, bg=self.colors['surface'])
        text_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        self.actions_text = scrolledtext.ScrolledText(text_container,
                                                     height=10,
                                                     font=("Consolas", 10),
                                                     bg='#FAFBFC',
                                                     fg=self.colors['text_primary'],
                                                     relief='solid',
                                                     bd=1,
                                                     wrap=tk.WORD)
        self.actions_text.pack(fill=tk.BOTH, expand=True)

        # 回放控制区域
        play_frame = tk.Frame(main_frame, bg=self.colors['surface'], relief='solid', bd=1)
        play_frame.pack(fill=tk.X)

        play_title = tk.Label(play_frame,
                             text="▶️ 回放控制",
                             font=("Microsoft YaHei UI", 12, "bold"),
                             fg=self.colors['text_primary'],
                             bg=self.colors['surface'])
        play_title.pack(pady=(15, 10))

        play_btn_frame = tk.Frame(play_frame, bg=self.colors['surface'])
        play_btn_frame.pack(pady=(0, 15))

        self.play_btn = tk.Button(play_btn_frame,
                                 text="▶️ 开始回放",
                                 command=self.start_playback,
                                 font=("Microsoft YaHei UI", 11, "bold"),
                                 bg=self.colors['success'],
                                 fg='white',
                                 relief='flat',
                                 padx=25, pady=10,
                                 cursor='hand2')
        self.play_btn.pack(side=tk.LEFT, padx=(0, 15))

        self.stop_btn = tk.Button(play_btn_frame,
                                 text="⏹️ 停止回放",
                                 command=self.stop_playback,
                                 font=("Microsoft YaHei UI", 11, "bold"),
                                 bg=self.colors['warning'],
                                 fg='white',
                                 relief='flat',
                                 padx=25, pady=10,
                                 cursor='hand2')
        self.stop_btn.pack(side=tk.LEFT)

        # 更新界面状态
        self.update_ui_state()
    
    def start_position_update(self):
        """启动鼠标位置更新线程"""
        def update_position():
            while True:
                try:
                    x, y = self.recorder.get_current_mouse_position()
                    self.position_label.config(text=f"X: {x}, Y: {y}")
                    time.sleep(0.1)
                except:
                    break
        
        thread = threading.Thread(target=update_position)
        thread.daemon = True
        thread.start()
    
    def toggle_recording(self):
        """切换录制状态"""
        if self.recorder.is_recording:
            self.recorder.stop_recording()
            self.record_btn.config(text="🔴 开始录制", bg=self.colors['primary'])
            self.status_label.config(text="🟠 录制已停止", fg=self.colors['warning'])
        else:
            if self.recorder.start_recording():
                self.record_btn.config(text="⏹️ 停止录制", bg=self.colors['warning'])
                self.status_label.config(text="🔴 正在录制... (可使用鼠标中键添加位置)", fg=self.colors['warning'])
            else:
                messagebox.showwarning("警告", "无法开始录制")

        self.update_ui_state()
    
    def add_current_position(self):
        """添加当前鼠标位置到录制列表"""
        if not self.recorder.is_recording:
            messagebox.showwarning("警告", "请先开始录制")
            return
        
        try:
            delay = float(self.delay_var.get())
        except ValueError:
            delay = 1.0
            self.delay_var.set("1.0")
        
        if self.recorder.record_current_position(delay):
            self.update_actions_display()
            self.update_step_count()

            x, y = self.recorder.get_current_mouse_position()
            self.status_label.config(
                text=f"✅ 已添加: ({x}, {y}) - 第{self.recorder.get_actions_count()}步",
                fg=self.colors['success']
            )

            if self.recorder.get_actions_count() >= 50:
                messagebox.showinfo("提示", "已达到最大步数限制(50步)")
                self.toggle_recording()
        else:
            messagebox.showwarning("警告", "无法添加更多步骤")
    
    def clear_recording(self):
        """清空录制"""
        if self.recorder.clear_actions():
            self.update_actions_display()
            self.update_step_count()
            self.status_label.config(text="🗑️ 录制已清空", fg=self.colors['primary'])
        else:
            messagebox.showwarning("警告", "无法清空录制（可能正在录制或回放中）")
    
    def start_playback(self):
        """开始回放"""
        if self.recorder.get_actions_count() == 0:
            messagebox.showwarning("警告", "没有录制的动作可以回放")
            return
        
        if self.recorder.is_recording:
            messagebox.showwarning("警告", "请先停止录制")
            return
        
        # 显示倒计时
        self.show_countdown()
    
    def show_countdown(self):
        """显示倒计时"""
        countdown_window = tk.Toplevel(self.root)
        countdown_window.title("准备回放")
        countdown_window.geometry("200x100")
        countdown_window.transient(self.root)
        countdown_window.grab_set()
        
        # 居中显示
        countdown_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 200,
            self.root.winfo_rooty() + 150
        ))
        
        countdown_label = ttk.Label(countdown_window, text="3", font=("Arial", 24, "bold"))
        countdown_label.pack(expand=True)
        
        def countdown(count):
            if count > 0:
                countdown_label.config(text=str(count))
                countdown_window.after(1000, lambda: countdown(count - 1))
            else:
                countdown_window.destroy()
                self.execute_playback()
        
        countdown(3)
    
    def execute_playback(self):
        """执行回放"""
        def status_callback(message):
            self.status_label.config(text=message, foreground="blue")
        
        if self.recorder.play_actions(status_callback):
            self.status_label.config(text="正在回放...", foreground="blue")
            self.update_ui_state()
        else:
            messagebox.showwarning("警告", "无法开始回放")
    
    def stop_playback(self):
        """停止回放"""
        self.recorder.stop_playing()
        self.status_label.config(text="回放已停止", foreground="orange")
        self.update_ui_state()
    
    def update_actions_display(self):
        """更新动作列表显示"""
        self.actions_text.delete(1.0, tk.END)
        self.actions_text.insert(1.0, self.recorder.get_actions_list())
    
    def update_step_count(self):
        """更新步数显示"""
        count = self.recorder.get_actions_count()
        self.step_label.config(text=f"已录制: {count}/50 步")
    
    def update_ui_state(self):
        """更新UI状态"""
        is_recording = self.recorder.is_recording
        is_playing = self.recorder.is_playing
        has_actions = self.recorder.get_actions_count() > 0

        # 更新按钮状态和颜色
        if is_recording:
            self.add_click_btn.config(state=tk.NORMAL, bg=self.colors['success'])
        else:
            self.add_click_btn.config(state=tk.DISABLED, bg='#CCCCCC')

        if not is_recording and not is_playing:
            self.clear_btn.config(state=tk.NORMAL, bg=self.colors['warning'])
        else:
            self.clear_btn.config(state=tk.DISABLED, bg='#CCCCCC')

        if has_actions and not is_recording and not is_playing:
            self.play_btn.config(state=tk.NORMAL, bg=self.colors['success'])
        else:
            self.play_btn.config(state=tk.DISABLED, bg='#CCCCCC')

        if is_playing:
            self.stop_btn.config(state=tk.NORMAL, bg=self.colors['warning'])
        else:
            self.stop_btn.config(state=tk.DISABLED, bg='#CCCCCC')
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = MouseAutomationGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")

if __name__ == "__main__":
    main()
