import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog
import threading
import time
from mouse_recorder import MouseRecorder

class MouseAutomationGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🖱️ 智能按键精灵 Pro - 鼠标自动化工具")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # 设置现代化主题色彩
        self.colors = {
            'primary': '#2E86AB',      # 主色调 - 蓝色
            'secondary': '#A23B72',    # 次要色 - 紫红色
            'success': '#F18F01',      # 成功色 - 橙色
            'warning': '#C73E1D',      # 警告色 - 红色
            'background': '#F5F7FA',   # 背景色 - 浅灰
            'surface': '#FFFFFF',      # 表面色 - 白色
            'text_primary': '#2D3748', # 主文本色
            'text_secondary': '#718096' # 次要文本色
        }

        # 设置窗口背景色
        self.root.configure(bg=self.colors['background'])

        # 创建鼠标录制器
        self.recorder = MouseRecorder()

        # 设置中键点击回调
        self.recorder.set_middle_click_callback(self.on_middle_click)

        # 设置热键回调
        self.recorder.set_hotkey_callbacks(self.hotkey_start_playback, self.hotkey_stop_playback)

        # 设置自定义热键回调
        self.recorder.set_custom_hotkey_callback(self.on_custom_hotkey)

        # 配置样式
        self.setup_styles()

        # 创建界面
        self.create_widgets()

        # 启动位置更新线程
        self.start_position_update()

    def setup_styles(self):
        """配置现代化样式"""
        style = ttk.Style()

        # 配置按钮样式
        style.configure('Primary.TButton',
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Microsoft YaHei UI', 10, 'bold'))

        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Microsoft YaHei UI', 10, 'bold'))

        style.configure('Warning.TButton',
                       background=self.colors['warning'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Microsoft YaHei UI', 10, 'bold'))

        # 配置标签框样式
        style.configure('Modern.TLabelframe',
                       background=self.colors['surface'],
                       borderwidth=1,
                       relief='solid')

        style.configure('Modern.TLabelframe.Label',
                       background=self.colors['surface'],
                       foreground=self.colors['text_primary'],
                       font=('Microsoft YaHei UI', 11, 'bold'))

    def on_middle_click(self):
        """处理鼠标中键点击事件"""
        if self.recorder.is_recording:
            # 添加当前位置
            try:
                delay = float(self.delay_var.get())
            except ValueError:
                delay = 1.0
                self.delay_var.set("1.0")

            if self.recorder.record_current_position(delay):
                self.update_actions_display()
                self.update_step_count()

                # 显示提示信息
                x, y = self.recorder.get_current_mouse_position()
                self.status_label.config(
                    text=f"✅ 中键添加: ({x}, {y}) - 第{self.recorder.get_actions_count()}步",
                    foreground=self.colors['success']
                )

                # 检查是否达到最大步数
                if self.recorder.get_actions_count() >= 50:
                    messagebox.showinfo("提示", "已达到最大步数限制(50步)")
                    self.toggle_recording()

    def hotkey_start_playback(self):
        """热键触发开始回放"""
        # 在主线程中执行UI操作
        self.root.after(0, self._safe_start_playback)

    def hotkey_stop_playback(self):
        """热键触发停止回放"""
        # 在主线程中执行UI操作
        self.root.after(0, self._safe_stop_playback)

    def _safe_start_playback(self):
        """安全的开始回放（在主线程中执行）"""
        if self.recorder.get_actions_count() == 0:
            self.status_label.config(text="⚠️ 热键触发失败: 没有录制的动作", fg=self.colors['warning'])
            return

        if self.recorder.is_recording:
            self.status_label.config(text="⚠️ 热键触发失败: 请先停止录制", fg=self.colors['warning'])
            return

        if self.recorder.is_playing:
            self.status_label.config(text="⚠️ 热键触发失败: 正在回放中", fg=self.colors['warning'])
            return

        # 显示热键触发提示
        self.status_label.config(text="🎹 F9热键触发回放...", fg=self.colors['primary'])

        # 延迟一秒后开始回放（给用户反应时间）
        self.root.after(1000, self.show_countdown)

    def _safe_stop_playback(self):
        """安全的停止回放（在主线程中执行）"""
        if not self.recorder.is_playing:
            self.status_label.config(text="⚠️ F10热键: 当前没有正在进行的回放", fg=self.colors['warning'])
            return

        self.recorder.stop_playing()
        self.status_label.config(text="🎹 F10热键停止回放", fg=self.colors['warning'])
        self.update_ui_state()

    def on_custom_hotkey(self, action):
        """处理自定义热键触发的动作"""
        # 在主线程中执行UI操作
        self.root.after(0, lambda: self._execute_custom_action(action))

    def _execute_custom_action(self, action):
        """执行自定义动作"""
        action_name = action.get('name', '未知动作')
        hotkey = action.get('hotkey', '未知')

        if self.recorder.is_recording:
            self.status_label.config(text=f"⚠️ 热键{hotkey}触发失败: 请先停止录制", fg=self.colors['warning'])
            return

        if self.recorder.is_playing:
            self.status_label.config(text=f"⚠️ 热键{hotkey}触发失败: 正在回放中", fg=self.colors['warning'])
            return

        # 显示热键触发提示
        self.status_label.config(text=f"🎹 热键{hotkey}触发动作: {action_name}", fg=self.colors['primary'])

        # 执行动作回放
        def status_callback(message):
            self.status_label.config(text=message, fg=self.colors['primary'])

        if self.recorder.play_saved_action(action_name, status_callback):
            self.update_ui_state()
        else:
            self.status_label.config(text=f"❌ 执行动作 {action_name} 失败", fg=self.colors['warning'])

    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = tk.Frame(self.root, bg=self.colors['background'], padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(fill=tk.X, pady=(0, 20))

        # 主标题
        title_label = tk.Label(title_frame,
                              text="🖱️ 智能按键精灵 Pro",
                              font=("Microsoft YaHei UI", 20, "bold"),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack()

        # 副标题
        subtitle_label = tk.Label(title_frame,
                                 text="动作保存 • 自定义热键 • 50步操作 • 中键录制 • 全局控制",
                                 font=("Microsoft YaHei UI", 10),
                                 fg=self.colors['text_secondary'],
                                 bg=self.colors['background'])
        subtitle_label.pack(pady=(5, 0))

        # 创建左右分栏
        content_frame = tk.Frame(main_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧面板 - 录制和回放控制
        left_panel = tk.Frame(content_frame, bg=self.colors['background'])
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 右侧面板 - 动作管理
        right_panel = tk.Frame(content_frame, bg=self.colors['background'])
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # 创建左侧内容
        self.create_left_panel(left_panel)

        # 创建右侧内容
        self.create_right_panel(right_panel)

    def create_left_panel(self, parent):
        """创建左侧面板内容"""
        # 鼠标位置显示区域
        position_frame = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
        position_frame.pack(fill=tk.X, pady=(0, 15))

        position_title = tk.Label(position_frame,
                                 text="📍 实时鼠标位置",
                                 font=("Microsoft YaHei UI", 12, "bold"),
                                 fg=self.colors['text_primary'],
                                 bg=self.colors['surface'])
        position_title.pack(pady=(10, 5))

        self.position_label = tk.Label(position_frame,
                                      text="X: 0, Y: 0",
                                      font=("Consolas", 14, "bold"),
                                      fg=self.colors['primary'],
                                      bg=self.colors['surface'])
        self.position_label.pack(pady=(0, 10))

        # 录制控制区域
        record_frame = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
        record_frame.pack(fill=tk.X, pady=(0, 15))

        record_title = tk.Label(record_frame,
                               text="🎬 录制控制中心",
                               font=("Microsoft YaHei UI", 12, "bold"),
                               fg=self.colors['text_primary'],
                               bg=self.colors['surface'])
        record_title.pack(pady=(15, 10))

        # 录制按钮行
        record_btn_frame = tk.Frame(record_frame, bg=self.colors['surface'])
        record_btn_frame.pack(pady=(0, 10))

        self.record_btn = tk.Button(record_btn_frame,
                                   text="🔴 开始录制",
                                   command=self.toggle_recording,
                                   font=("Microsoft YaHei UI", 11, "bold"),
                                   bg=self.colors['primary'],
                                   fg='white',
                                   relief='flat',
                                   padx=20, pady=8,
                                   cursor='hand2')
        self.record_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.add_click_btn = tk.Button(record_btn_frame,
                                      text="➕ 添加位置",
                                      command=self.add_current_position,
                                      font=("Microsoft YaHei UI", 10),
                                      bg=self.colors['success'],
                                      fg='white',
                                      relief='flat',
                                      padx=15, pady=8,
                                      cursor='hand2')
        self.add_click_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_btn = tk.Button(record_btn_frame,
                                  text="🗑️ 清空",
                                  command=self.clear_recording,
                                  font=("Microsoft YaHei UI", 10),
                                  bg=self.colors['warning'],
                                  fg='white',
                                  relief='flat',
                                  padx=15, pady=8,
                                  cursor='hand2')
        self.clear_btn.pack(side=tk.LEFT)

        # 延迟设置和提示
        delay_frame = tk.Frame(record_frame, bg=self.colors['surface'])
        delay_frame.pack(pady=(0, 10))

        delay_label = tk.Label(delay_frame,
                              text="⏱️ 点击间隔:",
                              font=("Microsoft YaHei UI", 10),
                              fg=self.colors['text_primary'],
                              bg=self.colors['surface'])
        delay_label.pack(side=tk.LEFT)

        self.delay_var = tk.StringVar(value="1.0")
        delay_spinbox = tk.Spinbox(delay_frame,
                                  from_=0.1, to=10.0, increment=0.1,
                                  width=8, textvariable=self.delay_var,
                                  font=("Consolas", 10))
        delay_spinbox.pack(side=tk.LEFT, padx=(10, 15))

        tip_label = tk.Label(delay_frame,
                            text="💡 提示: 录制时可使用鼠标中键快速添加位置",
                            font=("Microsoft YaHei UI", 9),
                            fg=self.colors['text_secondary'],
                            bg=self.colors['surface'])
        tip_label.pack(side=tk.LEFT)

        # 保存按钮
        save_frame = tk.Frame(record_frame, bg=self.colors['surface'])
        save_frame.pack(pady=(5, 15))

        self.save_action_btn = tk.Button(save_frame,
                                        text="💾 保存动作",
                                        command=self.save_current_action,
                                        font=("Microsoft YaHei UI", 10, "bold"),
                                        bg=self.colors['secondary'],
                                        fg='white',
                                        relief='flat',
                                        padx=20, pady=8,
                                        cursor='hand2')
        self.save_action_btn.pack()

        # 状态显示区域
        status_frame = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
        status_frame.pack(fill=tk.X, pady=(0, 15))

        status_title = tk.Label(status_frame,
                               text="📊 状态信息",
                               font=("Microsoft YaHei UI", 12, "bold"),
                               fg=self.colors['text_primary'],
                               bg=self.colors['surface'])
        status_title.pack(pady=(15, 10))

        status_content_frame = tk.Frame(status_frame, bg=self.colors['surface'])
        status_content_frame.pack(pady=(0, 15))

        self.status_label = tk.Label(status_content_frame,
                                    text="🟢 就绪",
                                    font=("Microsoft YaHei UI", 11, "bold"),
                                    fg=self.colors['success'],
                                    bg=self.colors['surface'])
        self.status_label.pack()

        self.step_label = tk.Label(status_content_frame,
                                  text="已录制: 0/50 步",
                                  font=("Microsoft YaHei UI", 10),
                                  fg=self.colors['text_secondary'],
                                  bg=self.colors['surface'])
        self.step_label.pack(pady=(5, 0))

        # 动作列表显示
        actions_frame = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
        actions_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        actions_title = tk.Label(actions_frame,
                                text="📝 当前录制步骤",
                                font=("Microsoft YaHei UI", 12, "bold"),
                                fg=self.colors['text_primary'],
                                bg=self.colors['surface'])
        actions_title.pack(pady=(15, 10))

        # 创建文本框容器
        text_container = tk.Frame(actions_frame, bg=self.colors['surface'])
        text_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        self.actions_text = scrolledtext.ScrolledText(text_container,
                                                     height=6,
                                                     font=("Consolas", 9),
                                                     bg='#FAFBFC',
                                                     fg=self.colors['text_primary'],
                                                     relief='solid',
                                                     bd=1,
                                                     wrap=tk.WORD)
        self.actions_text.pack(fill=tk.BOTH, expand=True)

        # 回放控制区域
        play_frame = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
        play_frame.pack(fill=tk.X)

        play_title = tk.Label(play_frame,
                             text="▶️ 回放控制",
                             font=("Microsoft YaHei UI", 12, "bold"),
                             fg=self.colors['text_primary'],
                             bg=self.colors['surface'])
        play_title.pack(pady=(15, 10))

        play_btn_frame = tk.Frame(play_frame, bg=self.colors['surface'])
        play_btn_frame.pack(pady=(0, 15))

        self.play_btn = tk.Button(play_btn_frame,
                                 text="▶️ 开始回放",
                                 command=self.start_playback,
                                 font=("Microsoft YaHei UI", 11, "bold"),
                                 bg=self.colors['success'],
                                 fg='white',
                                 relief='flat',
                                 padx=25, pady=10,
                                 cursor='hand2')
        self.play_btn.pack(side=tk.LEFT, padx=(0, 15))

        self.stop_btn = tk.Button(play_btn_frame,
                                 text="⏹️ 停止回放",
                                 command=self.stop_playback,
                                 font=("Microsoft YaHei UI", 11, "bold"),
                                 bg=self.colors['warning'],
                                 fg='white',
                                 relief='flat',
                                 padx=25, pady=10,
                                 cursor='hand2')
        self.stop_btn.pack(side=tk.LEFT)

        # 热键提示
        hotkey_tip_frame = tk.Frame(play_frame, bg=self.colors['surface'])
        hotkey_tip_frame.pack(pady=(10, 15))

        hotkey_tip_label = tk.Label(hotkey_tip_frame,
                                   text="🎹 全局热键: F9-开始回放 | F10-停止回放 (程序后台运行时也可使用)",
                                   font=("Microsoft YaHei UI", 9),
                                   fg=self.colors['text_secondary'],
                                   bg=self.colors['surface'])
        hotkey_tip_label.pack()

        # 更新界面状态
        self.update_ui_state()

    def create_right_panel(self, parent):
        """创建右侧面板内容 - 动作管理"""
        # 动作管理标题
        title_frame = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        title_label = tk.Label(title_frame,
                              text="🎮 动作管理中心",
                              font=("Microsoft YaHei UI", 14, "bold"),
                              fg=self.colors['primary'],
                              bg=self.colors['surface'])
        title_label.pack(pady=(15, 15))

        # 动作列表区域
        list_frame = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        list_title = tk.Label(list_frame,
                             text="📋 保存的动作列表",
                             font=("Microsoft YaHei UI", 12, "bold"),
                             fg=self.colors['text_primary'],
                             bg=self.colors['surface'])
        list_title.pack(pady=(15, 10))

        # 创建列表框和滚动条
        list_container = tk.Frame(list_frame, bg=self.colors['surface'])
        list_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # 列表框
        self.action_listbox = tk.Listbox(list_container,
                                        font=("Microsoft YaHei UI", 10),
                                        bg='#FAFBFC',
                                        fg=self.colors['text_primary'],
                                        selectbackground=self.colors['primary'],
                                        selectforeground='white',
                                        relief='solid',
                                        bd=1,
                                        height=8)
        self.action_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        scrollbar = tk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.action_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.action_listbox.config(yscrollcommand=scrollbar.set)

        # 绑定选择事件
        self.action_listbox.bind('<<ListboxSelect>>', self.on_action_select)

        # 动作详情和热键设置区域
        detail_frame = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
        detail_frame.pack(fill=tk.X, pady=(0, 15))

        detail_title = tk.Label(detail_frame,
                               text="🔧 动作详情与热键设置",
                               font=("Microsoft YaHei UI", 12, "bold"),
                               fg=self.colors['text_primary'],
                               bg=self.colors['surface'])
        detail_title.pack(pady=(15, 10))

        # 动作详情显示
        self.detail_text = scrolledtext.ScrolledText(detail_frame,
                                                    height=6,
                                                    font=("Consolas", 9),
                                                    bg='#FAFBFC',
                                                    fg=self.colors['text_primary'],
                                                    relief='solid',
                                                    bd=1,
                                                    wrap=tk.WORD,
                                                    state=tk.DISABLED)
        self.detail_text.pack(fill=tk.X, padx=15, pady=(0, 10))

        # 热键设置区域
        hotkey_frame = tk.Frame(detail_frame, bg=self.colors['surface'])
        hotkey_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        hotkey_label = tk.Label(hotkey_frame,
                               text="🎹 热键设置:",
                               font=("Microsoft YaHei UI", 10),
                               fg=self.colors['text_primary'],
                               bg=self.colors['surface'])
        hotkey_label.pack(side=tk.LEFT)

        self.hotkey_var = tk.StringVar()
        self.hotkey_combo = ttk.Combobox(hotkey_frame,
                                        textvariable=self.hotkey_var,
                                        width=10,
                                        font=("Consolas", 10),
                                        state="readonly")
        self.hotkey_combo.pack(side=tk.LEFT, padx=(10, 10))

        self.set_hotkey_btn = tk.Button(hotkey_frame,
                                       text="设置热键",
                                       command=self.set_action_hotkey,
                                       font=("Microsoft YaHei UI", 9),
                                       bg=self.colors['primary'],
                                       fg='white',
                                       relief='flat',
                                       padx=10, pady=5,
                                       cursor='hand2')
        self.set_hotkey_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 动作管理按钮
        btn_frame = tk.Frame(detail_frame, bg=self.colors['surface'])
        btn_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        self.play_action_btn = tk.Button(btn_frame,
                                        text="▶️ 执行动作",
                                        command=self.play_selected_action,
                                        font=("Microsoft YaHei UI", 10),
                                        bg=self.colors['success'],
                                        fg='white',
                                        relief='flat',
                                        padx=15, pady=5,
                                        cursor='hand2')
        self.play_action_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.delete_action_btn = tk.Button(btn_frame,
                                          text="🗑️ 删除动作",
                                          command=self.delete_selected_action,
                                          font=("Microsoft YaHei UI", 10),
                                          bg=self.colors['warning'],
                                          fg='white',
                                          relief='flat',
                                          padx=15, pady=5,
                                          cursor='hand2')
        self.delete_action_btn.pack(side=tk.LEFT)

        # 加载保存的动作
        self.refresh_action_list()
        self.refresh_hotkey_combo()

    def start_position_update(self):
        """启动鼠标位置更新线程"""
        def update_position():
            while True:
                try:
                    x, y = self.recorder.get_current_mouse_position()
                    self.position_label.config(text=f"X: {x}, Y: {y}")
                    time.sleep(0.1)
                except:
                    break
        
        thread = threading.Thread(target=update_position)
        thread.daemon = True
        thread.start()
    
    def toggle_recording(self):
        """切换录制状态"""
        if self.recorder.is_recording:
            self.recorder.stop_recording()
            self.record_btn.config(text="🔴 开始录制", bg=self.colors['primary'])
            self.status_label.config(text="🟠 录制已停止", fg=self.colors['warning'])
        else:
            if self.recorder.start_recording():
                self.record_btn.config(text="⏹️ 停止录制", bg=self.colors['warning'])
                self.status_label.config(text="🔴 正在录制... (可使用鼠标中键添加位置)", fg=self.colors['warning'])
            else:
                messagebox.showwarning("警告", "无法开始录制")

        self.update_ui_state()
    
    def add_current_position(self):
        """添加当前鼠标位置到录制列表"""
        if not self.recorder.is_recording:
            messagebox.showwarning("警告", "请先开始录制")
            return
        
        try:
            delay = float(self.delay_var.get())
        except ValueError:
            delay = 1.0
            self.delay_var.set("1.0")
        
        if self.recorder.record_current_position(delay):
            self.update_actions_display()
            self.update_step_count()

            x, y = self.recorder.get_current_mouse_position()
            self.status_label.config(
                text=f"✅ 已添加: ({x}, {y}) - 第{self.recorder.get_actions_count()}步",
                fg=self.colors['success']
            )

            if self.recorder.get_actions_count() >= 50:
                messagebox.showinfo("提示", "已达到最大步数限制(50步)")
                self.toggle_recording()
        else:
            messagebox.showwarning("警告", "无法添加更多步骤")
    
    def clear_recording(self):
        """清空录制"""
        if self.recorder.clear_actions():
            self.update_actions_display()
            self.update_step_count()
            self.status_label.config(text="🗑️ 录制已清空", fg=self.colors['primary'])
        else:
            messagebox.showwarning("警告", "无法清空录制（可能正在录制或回放中）")
    
    def start_playback(self):
        """开始回放"""
        if self.recorder.get_actions_count() == 0:
            messagebox.showwarning("警告", "没有录制的动作可以回放")
            return
        
        if self.recorder.is_recording:
            messagebox.showwarning("警告", "请先停止录制")
            return
        
        # 显示倒计时
        self.show_countdown()
    
    def show_countdown(self):
        """显示倒计时"""
        countdown_window = tk.Toplevel(self.root)
        countdown_window.title("准备回放")
        countdown_window.geometry("200x100")
        countdown_window.transient(self.root)
        countdown_window.grab_set()
        
        # 居中显示
        countdown_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 200,
            self.root.winfo_rooty() + 150
        ))
        
        countdown_label = ttk.Label(countdown_window, text="3", font=("Arial", 24, "bold"))
        countdown_label.pack(expand=True)
        
        def countdown(count):
            if count > 0:
                countdown_label.config(text=str(count))
                countdown_window.after(1000, lambda: countdown(count - 1))
            else:
                countdown_window.destroy()
                self.execute_playback()
        
        countdown(3)
    
    def execute_playback(self):
        """执行回放"""
        def status_callback(message):
            self.status_label.config(text=message, foreground="blue")
        
        if self.recorder.play_actions(status_callback):
            self.status_label.config(text="正在回放...", foreground="blue")
            self.update_ui_state()
        else:
            messagebox.showwarning("警告", "无法开始回放")
    
    def stop_playback(self):
        """停止回放"""
        self.recorder.stop_playing()
        self.status_label.config(text="回放已停止", foreground="orange")
        self.update_ui_state()
    
    def update_actions_display(self):
        """更新动作列表显示"""
        self.actions_text.delete(1.0, tk.END)
        self.actions_text.insert(1.0, self.recorder.get_actions_list())
    
    def update_step_count(self):
        """更新步数显示"""
        count = self.recorder.get_actions_count()
        self.step_label.config(text=f"已录制: {count}/50 步")
    
    def update_ui_state(self):
        """更新UI状态"""
        is_recording = self.recorder.is_recording
        is_playing = self.recorder.is_playing
        has_actions = self.recorder.get_actions_count() > 0

        # 更新按钮状态和颜色
        if is_recording:
            self.add_click_btn.config(state=tk.NORMAL, bg=self.colors['success'])
        else:
            self.add_click_btn.config(state=tk.DISABLED, bg='#CCCCCC')

        if not is_recording and not is_playing:
            self.clear_btn.config(state=tk.NORMAL, bg=self.colors['warning'])
        else:
            self.clear_btn.config(state=tk.DISABLED, bg='#CCCCCC')

        if has_actions and not is_recording and not is_playing:
            self.play_btn.config(state=tk.NORMAL, bg=self.colors['success'])
            self.save_action_btn.config(state=tk.NORMAL, bg=self.colors['secondary'])
        else:
            self.play_btn.config(state=tk.DISABLED, bg='#CCCCCC')
            self.save_action_btn.config(state=tk.DISABLED, bg='#CCCCCC')

        if is_playing:
            self.stop_btn.config(state=tk.NORMAL, bg=self.colors['warning'])
        else:
            self.stop_btn.config(state=tk.DISABLED, bg='#CCCCCC')

    def save_current_action(self):
        """保存当前录制的动作"""
        if self.recorder.get_actions_count() == 0:
            messagebox.showwarning("警告", "没有录制的动作可以保存")
            return

        # 获取动作名称
        name = simpledialog.askstring("保存动作", "请输入动作名称:",
                                     initialvalue=f"动作_{len(self.recorder.action_manager.get_all_actions()) + 1}")
        if not name:
            return

        # 检查名称是否已存在
        if self.recorder.action_manager.get_action(name):
            if not messagebox.askyesno("确认", f"动作 '{name}' 已存在，是否覆盖？"):
                return

        # 获取描述
        description = simpledialog.askstring("动作描述", "请输入动作描述（可选）:", initialvalue="")

        # 保存动作
        steps = self.recorder.actions.copy()
        if self.recorder.action_manager.add_action(name, steps, description=description or ""):
            self.status_label.config(text=f"✅ 动作 '{name}' 保存成功", fg=self.colors['success'])
            self.refresh_action_list()
            self.refresh_hotkey_combo()
        else:
            messagebox.showerror("错误", "保存动作失败")

    def refresh_action_list(self):
        """刷新动作列表"""
        self.action_listbox.delete(0, tk.END)
        actions = self.recorder.action_manager.get_all_actions()

        for name, action in actions.items():
            hotkey = action.get('hotkey', '')
            hotkey_text = f" [{hotkey}]" if hotkey else ""
            use_count = action.get('use_count', 0)
            display_text = f"{name}{hotkey_text} (使用{use_count}次)"
            self.action_listbox.insert(tk.END, display_text)

    def refresh_hotkey_combo(self):
        """刷新热键下拉框"""
        available_hotkeys = self.recorder.action_manager.get_available_hotkeys()
        self.hotkey_combo['values'] = available_hotkeys
        if available_hotkeys:
            self.hotkey_var.set(available_hotkeys[0])

    def on_action_select(self, event):
        """动作选择事件处理"""
        selection = self.action_listbox.curselection()
        if not selection:
            return

        # 获取选中的动作名称
        selected_text = self.action_listbox.get(selection[0])
        action_name = selected_text.split(' [')[0].split(' (')[0]  # 提取动作名称

        # 显示动作详情
        action = self.recorder.action_manager.get_action(action_name)
        if action:
            self.show_action_details(action)

    def show_action_details(self, action):
        """显示动作详情"""
        self.detail_text.config(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)

        # 基本信息
        name = action.get('name', '未知')
        description = action.get('description', '无描述')
        hotkey = action.get('hotkey', '未设置')
        use_count = action.get('use_count', 0)
        created_time = action.get('created_time', '未知')

        info_text = f"动作名称: {name}\n"
        info_text += f"描述: {description}\n"
        info_text += f"热键: {hotkey}\n"
        info_text += f"使用次数: {use_count}\n"
        info_text += f"创建时间: {created_time[:19] if created_time != '未知' else '未知'}\n"
        info_text += f"\n步骤详情:\n"

        # 步骤信息
        steps = action.get('steps', [])
        if steps:
            for i, (x, y, delay) in enumerate(steps):
                info_text += f"第{i+1}步: 点击 ({x}, {y}) 延迟 {delay}秒\n"
        else:
            info_text += "暂无步骤\n"

        self.detail_text.insert(1.0, info_text)
        self.detail_text.config(state=tk.DISABLED)

    def set_action_hotkey(self):
        """设置动作热键"""
        selection = self.action_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个动作")
            return

        # 获取选中的动作名称
        selected_text = self.action_listbox.get(selection[0])
        action_name = selected_text.split(' [')[0].split(' (')[0]

        # 获取选择的热键
        hotkey = self.hotkey_var.get()
        if not hotkey:
            messagebox.showwarning("警告", "请选择一个热键")
            return

        # 设置热键
        if self.recorder.action_manager.update_action_hotkey(action_name, hotkey):
            self.status_label.config(text=f"✅ 动作 '{action_name}' 热键设置为 '{hotkey}'", fg=self.colors['success'])
            self.refresh_action_list()
            self.refresh_hotkey_combo()

            # 重新显示详情
            action = self.recorder.action_manager.get_action(action_name)
            if action:
                self.show_action_details(action)
        else:
            messagebox.showerror("错误", f"热键 '{hotkey}' 已被其他动作使用")

    def play_selected_action(self):
        """执行选中的动作"""
        selection = self.action_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个动作")
            return

        # 获取选中的动作名称
        selected_text = self.action_listbox.get(selection[0])
        action_name = selected_text.split(' [')[0].split(' (')[0]

        # 检查状态
        if self.recorder.is_recording:
            messagebox.showwarning("警告", "请先停止录制")
            return

        if self.recorder.is_playing:
            messagebox.showwarning("警告", "正在回放中，请稍候")
            return

        # 执行动作
        def status_callback(message):
            self.status_label.config(text=message, fg=self.colors['primary'])

        if self.recorder.play_saved_action(action_name, status_callback):
            self.update_ui_state()
            # 刷新列表以更新使用次数
            self.refresh_action_list()
        else:
            messagebox.showerror("错误", f"执行动作 '{action_name}' 失败")

    def delete_selected_action(self):
        """删除选中的动作"""
        selection = self.action_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个动作")
            return

        # 获取选中的动作名称
        selected_text = self.action_listbox.get(selection[0])
        action_name = selected_text.split(' [')[0].split(' (')[0]

        # 确认删除
        if messagebox.askyesno("确认删除", f"确定要删除动作 '{action_name}' 吗？"):
            if self.recorder.action_manager.remove_action(action_name):
                self.status_label.config(text=f"✅ 动作 '{action_name}' 已删除", fg=self.colors['success'])
                self.refresh_action_list()
                self.refresh_hotkey_combo()

                # 清空详情显示
                self.detail_text.config(state=tk.NORMAL)
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.config(state=tk.DISABLED)
            else:
                messagebox.showerror("错误", f"删除动作 '{action_name}' 失败")

    def on_closing(self):
        """程序关闭时的清理工作"""
        # 停止所有监听器
        self.recorder.stop_keyboard_listener()
        if self.recorder.mouse_listener:
            self.recorder.mouse_listener.stop()

        # 关闭窗口
        self.root.destroy()

    def run(self):
        """运行应用程序"""
        # 设置关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = MouseAutomationGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")

if __name__ == "__main__":
    main()
